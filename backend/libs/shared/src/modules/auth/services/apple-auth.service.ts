import {
  HttpException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import SignInWithAppleResponse from 'apple-signin-auth';
import { ConfigService } from '@nestjs/config';

export interface AppleUserProfile {
  id: string;
  email: string;
  name: string;
}

export interface AuthAppleLoginDto {
  identityToken: string;
}

@Injectable()
export class AppleAuthService {
  constructor(
    private readonly configService: ConfigService,
  ) { }
  async getProfileByToken(
    loginDto: AuthAppleLoginDto,
  ): Promise<AppleUserProfile> {
    try {
      const response = await SignInWithAppleResponse.verifyIdToken(
        loginDto.identityToken,
        {
          audience: this.configService.getOrThrow<string>('APPLE_BUNDLE_ID'),
        },
      );

      if (!response) {
        throw new UnauthorizedException('Invalid Apple token payload.');
      }

      const name = response.email?.split('@')[0] || '';

      return {
        id: response.sub,
        email: response.email || '',
        name,
      };
    } catch (error) {
      this.handleError(error, 'Failed to verify Apple token');
    }
  }

  private handleError(error: unknown, fallbackMessage: string): never {
    if (error instanceof HttpException) {
      throw error;
    }

    if (error instanceof Error) {
      throw new UnauthorizedException(error.message || fallbackMessage);
    }

    throw new UnauthorizedException(fallbackMessage);
  }
}
