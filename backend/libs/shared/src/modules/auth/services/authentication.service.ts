import { Injectable, NotFoundException, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { UserRepository } from '../../../repositories/user.repository';
import { RoleRepository, UserRoleRepository } from '../../../repositories';
import { UserProfileRepository } from '../../../repositories/user-profile.repository';
import { AuthProvider, AuthRole } from '../../../common/constants/constants';
import { AuthTokensWithProfileDto } from 'apps/api/src/v1/auth/dto/auth-tokens-with-profile.dto';
import { AuthRoleService } from '../auth-role.service';
import { OtpService } from './otp.service';
import { OAuthService, OAuthUserData } from './oauth.service';
import { TokenService } from './token.service';
import { UserRegistrationService } from './user-registration.service';

@Injectable()
export class AuthenticationService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly roleRepository: RoleRepository,
    private readonly userRoleRepository: UserRoleRepository,
    private readonly userProfileRepository: UserProfileRepository,
    private readonly authRoleService: AuthRoleService,
    private readonly otpService: OtpService,
    private readonly oauthService: OAuthService,
    private readonly tokenService: TokenService,
    private readonly userRegistrationService: UserRegistrationService,
  ) {}

  /**
   * Ensure user has role and get user profile
   * @param userId User ID
   * @param roleName Role name
   * @returns User profile for the role
   */
  private async ensureUserRoleAndProfile(userId: string, roleName: string) {
    const role = await this.roleRepository.findByName(roleName);
    if (!role) {
      throw new NotFoundException('Role not found');
    }

    // Check if user has this role assigned, if not assign it
    const hasRole = await this.userRoleRepository.userHasRole(userId, roleName);
    if (!hasRole) {
      await this.authRoleService.assignRoleToUser(userId, roleName as any);
    }

    // Find or create user profile for this role
    const userProfile = await this.userProfileRepository.findOrCreateByUserIdAndRoleId(
      userId,
      role.id,
    );

    return { role, userProfile };
  }

  /**
   * Login with phone number and OTP
   * @param phoneNumber Phone number in international format
   * @param otp OTP code
   * @param roleName Role name
   * @returns Auth tokens
   */
  async loginWithPhone(
    phoneNumber: string,
    otp: string,
    roleName?: string,
  ): Promise<AuthTokensWithProfileDto> {
    const isValid = await this.otpService.verifyPhoneOtp(phoneNumber, otp);
    if (!isValid) {
      throw new UnauthorizedException('Invalid OTP');
    }

    const user = await this.userRepository.findByPhoneNumber(phoneNumber);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!roleName) {
      throw new BadRequestException('Role name is required');
    }

    const { role, userProfile } = await this.ensureUserRoleAndProfile(user.id, roleName);

    return this.tokenService.generateTokens(user.id, role, userProfile);
  }

  /**
   * Login with email address and OTP
   * @param email Email address
   * @param otp OTP code
   * @param roleName Role name
   * @returns Auth tokens
   */
  async loginWithEmail(
    email: string,
    otp: string,
    roleName?: string,
  ): Promise<AuthTokensWithProfileDto> {
    const isValid = await this.otpService.verifyEmailOtp(email, otp);
    if (!isValid) {
      throw new UnauthorizedException('Invalid OTP');
    }

    const user = await this.userRepository.findByEmail(email);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!roleName) {
      throw new BadRequestException('Role name is required');
    }

    const { role, userProfile } = await this.ensureUserRoleAndProfile(user.id, roleName);

    return this.tokenService.generateTokens(user.id, role, userProfile);
  }

  /**
   * Login with OAuth provider
   * @param provider OAuth provider (google, apple)
   * @param accessToken OAuth access token
   * @param role Role for the user
   * @returns Auth tokens
   */
  async loginWithOAuth(
    provider: AuthProvider,
    accessToken: string,
    role?: AuthRole,
  ): Promise<AuthTokensWithProfileDto> {
    const userData: OAuthUserData = await this.oauthService.verifyOAuthToken(provider, accessToken);

    if (!userData || !userData.email) {
      throw new UnauthorizedException('Invalid OAuth token');
    }

    const user = await this.userRegistrationService.registerWithOAuth(provider, userData, role);

    const roleName = role || AuthRole.RIDER;
    const { role: roleObj, userProfile } = await this.ensureUserRoleAndProfile(user.id, roleName);

    return this.tokenService.generateTokens(user.id, roleObj, userProfile);
  }

  /**
   * Refresh access token using refresh token
   * @param refreshToken Refresh token
   * @returns New access token, refresh token, and expiry
   */
  async refreshToken(refreshToken: string): Promise<AuthTokensWithProfileDto> {
    const userId = await this.tokenService.validateAndRevokeRefreshToken(refreshToken);

    // Get user roles to determine the role for the token
    const userRoles = await this.authRoleService.getUserRoles(userId);

    if (userRoles.length === 0) {
      throw new UnauthorizedException('User has no assigned roles');
    }

    const roleName = userRoles[0];
    const role = await this.roleRepository.findByName(roleName);
    if (!role) {
      throw new NotFoundException(`Role '${roleName}' not found`);
    }

    // Get user profile for this role
    const userProfile = await this.userProfileRepository.getOneByUserIdAndRoleId(
      userId,
      role.id,
    );

    return this.tokenService.generateTokens(userId, role, userProfile || undefined);
  }
}
